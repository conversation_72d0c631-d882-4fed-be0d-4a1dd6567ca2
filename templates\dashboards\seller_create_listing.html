{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}Create Listing{% endblock %}
{% block active_nav_item %}create{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Seller Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='seller_dashboard' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- My Listings -->
    {% include 'components/sidebar_nav_item.html' with href='seller_listings' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='My Listings' item_key='listings' %}

    <!-- Create Listing -->
    {% include 'components/sidebar_nav_item.html' with href='seller_create_listing' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>' label='Create Listing' item_key='create' %}

    <!-- Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='seller_inquiries' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='Inquiries' item_key='inquiries' %}

    <!-- Divider -->
    <div class="border-t border-secondary-200 my-4"></div>

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='seller_reports' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' label='Help & Support' item_key='help' %}
</div>
{% endblock %}

{% block page_title %}Create New Listing{% endblock %}
{% block page_description %}Add a new land property to the marketplace{% endblock %}

{% block page_actions %}
<a href="{% url 'seller_listings' %}" class="btn btn-secondary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
    </svg>
    Back to Listings
</a>
{% endblock %}

{% block dashboard_content %}
<form method="post" enctype="multipart/form-data" class="max-w-4xl mx-auto" x-data="listingForm()">
    {% csrf_token %}
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Form -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Basic Information</h3>
                    <p class="text-sm text-secondary-600">Provide essential details about your land</p>
                </div>
                <div class="card-body space-y-4">
                    <div>
                        <label class="form-label">{{ form.title.label }}</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="form-error">{{ form.title.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="form-error">{{ form.description.errors.0 }}</div>
                        {% endif %}
                        <p class="text-xs text-secondary-500 mt-1">Describe the land features, potential uses, nearby amenities, and any other relevant details</p>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="form-label">{{ form.property_type.label }}</label>
                            {{ form.property_type }}
                            {% if form.property_type.errors %}
                                <div class="form-error">{{ form.property_type.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div>
                            <label class="form-label">{{ form.size_acres.label }}</label>
                            {{ form.size_acres }}
                            {% if form.size_acres.errors %}
                                <div class="form-error">{{ form.size_acres.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location & Pricing -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Location & Pricing</h3>
                    <p class="text-sm text-secondary-600">Specify where your land is located and set your price</p>
                </div>
                <div class="card-body space-y-4">
                    <div>
                        <label class="form-label">{{ form.location.label }}</label>
                        {{ form.location }}
                        {% if form.location.errors %}
                            <div class="form-error">{{ form.location.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="form-label">{{ form.address.label }}</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="form-error">{{ form.address.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="form-label">{{ form.price.label }}</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-secondary-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" name="price" step="0.01" min="0" class="form-input pl-7" placeholder="0.00" value="{{ form.price.value|default:'' }}" x-model="price" @input="calculatePricePerAcre()">
                        </div>
                        {% if form.price.errors %}
                            <div class="form-error">{{ form.price.errors.0 }}</div>
                        {% endif %}
                        <div x-show="pricePerAcre > 0" class="text-sm text-secondary-600 mt-1">
                            Price per acre: $<span x-text="pricePerAcre.toLocaleString()"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Images -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Property Images</h3>
                    <p class="text-sm text-secondary-600">Upload photos to showcase your land (max 10 images, 5MB each)</p>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <!-- Drag and Drop Area -->
                        <div x-data="imageUpload()" class="border-2 border-dashed border-secondary-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors duration-200"
                             @dragover.prevent="dragover = true"
                             @dragleave.prevent="dragover = false"
                             @drop.prevent="handleDrop($event)"
                             :class="{ 'border-primary-400 bg-primary-50': dragover }">
                            
                            <input type="file" name="images" multiple accept="image/*" class="hidden" x-ref="fileInput" @change="handleFileSelect($event)">
                            
                            <div class="space-y-4">
                                <svg class="w-12 h-12 text-secondary-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <div>
                                    <p class="text-secondary-600">Drag and drop images here, or</p>
                                    <button type="button" @click="$refs.fileInput.click()" class="btn btn-secondary btn-sm mt-2">Browse Files</button>
                                </div>
                                <p class="text-xs text-secondary-500">PNG, JPG, GIF up to 5MB each</p>
                            </div>
                        </div>

                        <!-- Image Previews -->
                        <div x-show="selectedImages.length > 0" class="grid grid-cols-2 md:grid-cols-3 gap-4">
                            <template x-for="(image, index) in selectedImages" :key="index">
                                <div class="relative group">
                                    <img :src="image.url" :alt="'Preview ' + (index + 1)" class="w-full h-32 object-cover rounded-lg">
                                    <button type="button" @click="removeImage(index)" class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                    <div x-show="index === 0" class="absolute bottom-2 left-2 bg-primary-500 text-white text-xs px-2 py-1 rounded">Primary</div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Preview Card -->
            <div class="card sticky top-6">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Listing Preview</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="w-full h-32 bg-secondary-100 rounded-lg flex items-center justify-center">
                            <template x-if="selectedImages.length > 0">
                                <img :src="selectedImages[0].url" alt="Primary image" class="w-full h-full object-cover rounded-lg">
                            </template>
                            <template x-if="selectedImages.length === 0">
                                <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </template>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-secondary-900" x-text="title || 'Your listing title'"></h4>
                            <p class="text-sm text-secondary-600" x-text="location || 'Location'"></p>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-lg font-bold text-secondary-900">$<span x-text="price ? parseFloat(price).toLocaleString() : '0'"></span></p>
                                <p class="text-sm text-secondary-500"><span x-text="size || '0'"></span> acres</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-secondary-700">$<span x-text="pricePerAcre.toLocaleString()"></span>/acre</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h4 class="text-sm font-medium text-blue-900">Review Process</h4>
                                    <p class="text-sm text-blue-700 mt-1">Your listing will be submitted for admin approval before going live.</p>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="w-full btn btn-primary btn-md">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            Submit for Review
                        </button>
                        
                        <a href="{% url 'seller_listings' %}" class="w-full btn btn-secondary btn-md">Cancel</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('listingForm', () => ({
        title: '',
        location: '',
        price: 0,
        size: 0,
        pricePerAcre: 0,
        
        init() {
            this.$watch('title', value => this.title = value);
            this.$watch('location', value => this.location = value);
        },
        
        calculatePricePerAcre() {
            const sizeInput = document.querySelector('input[name="size_acres"]');
            this.size = parseFloat(sizeInput?.value) || 0;
            this.pricePerAcre = this.size > 0 ? this.price / this.size : 0;
        }
    }));
    
    Alpine.data('imageUpload', () => ({
        selectedImages: [],
        dragover: false,
        
        handleFileSelect(event) {
            this.processFiles(event.target.files);
        },
        
        handleDrop(event) {
            this.dragover = false;
            this.processFiles(event.dataTransfer.files);
        },
        
        processFiles(files) {
            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/') && this.selectedImages.length < 10) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.selectedImages.push({
                            file: file,
                            url: e.target.result,
                            name: file.name
                        });
                    };
                    reader.readAsDataURL(file);
                }
            });
        },
        
        removeImage(index) {
            this.selectedImages.splice(index, 1);
        }
    }));
});
</script>
{% endblock %}
{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}Inquiries{% endblock %}
{% block active_nav_item %}inquiries{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Seller Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='seller_dashboard' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- My Listings -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_listings" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='My Listings' item_key='listings' %}

    <!-- Create Listing -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_create_listing" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>' label='Create Listing' item_key='create' %}

    <!-- Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_inquiries" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='Inquiries' item_key='inquiries' %}

    <!-- Divider -->
    <div class="border-t border-secondary-200 my-4"></div>

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='#help' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' label='Help & Support' item_key='help' %}
</div>
{% endblock %}

{% block page_title %}Inquiries{% endblock %}
{% block page_description %}Manage buyer inquiries about your listings{% endblock %}

{% block dashboard_content %}
<!-- Filters -->
<div class="bg-white rounded-lg shadow-sm border border-secondary-200 p-6 mb-6">
    <form method="get" class="flex flex-col md:flex-row gap-4" hx-get="{% url 'seller_inquiries' %}" hx-target="#inquiries-container" hx-trigger="change, submit">
        <div class="w-full md:w-48">
            <select name="status" class="form-select">
                <option value="">All Inquiries</option>
                <option value="unread" {% if status_filter == 'unread' %}selected{% endif %}>Unread</option>
                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending Response</option>
                <option value="responded" {% if status_filter == 'responded' %}selected{% endif %}>Responded</option>
            </select>
        </div>
        <div class="w-full md:w-64">
            <select name="listing" class="form-select">
                <option value="">All Listings</option>
                {% for listing in seller_listings %}
                    <option value="{{ listing.id }}"{% if listing_filter == listing.id|stringformat:"s" %} selected{% endif %}>{{ listing.title|truncatechars:30 }}</option>
                {% endfor %}
            </select>
        </div>
        <button type="submit" class="btn btn-secondary btn-md">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            Filter
        </button>
    </form>
</div>

<!-- Inquiries List -->
<div id="inquiries-container">
    {% if page_obj %}
        <div class="space-y-4 mb-8">
            {% for inquiry in page_obj %}
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="card-body">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start space-x-4 flex-1">
                            <!-- Buyer Avatar -->
                            <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-primary-600 font-medium">{{ inquiry.buyer.first_name.0|default:inquiry.buyer.username.0|upper }}</span>
                            </div>
                            
                            <!-- Inquiry Content -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center space-x-2 mb-2">
                                    <h3 class="text-lg font-semibold text-secondary-900">{{ inquiry.subject }}</h3>
                                    {% include 'components/inquiry_status_badge.html' with inquiry=inquiry %}
                                </div>
                                
                                <div class="flex items-center space-x-4 text-sm text-secondary-600 mb-3">
                                    <span>From: <strong>{{ inquiry.buyer.get_full_name|default:inquiry.buyer.username }}</strong></span>
                                    <span>•</span>
                                    <span>{{ inquiry.created_at|timesince }} ago</span>
                                    <span>•</span>
                                    <span>Property: <strong>{{ inquiry.land.title|truncatechars:30 }}</strong></span>
                                </div>
                                
                                <p class="text-secondary-700 mb-4">{{ inquiry.message|truncatechars:200 }}</p>
                                
                                {% if inquiry.seller_response %}
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                                        <div class="flex items-center mb-2">
                                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-sm font-medium text-green-800">Your Response ({{ inquiry.response_date|timesince }} ago)</span>
                                        </div>
                                        <p class="text-sm text-green-700">{{ inquiry.seller_response|truncatechars:150 }}</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex items-center space-x-2 ml-4">
                            {% if not inquiry.is_read %}
                                <button hx-post="{% url 'seller_mark_inquiry_read' inquiry.id %}" 
                                        hx-target="closest .card"
                                        hx-swap="outerHTML"
                                        class="btn btn-ghost btn-sm" 
                                        title="Mark as read">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </button>
                            {% endif %}
                            
                            <a href="{% url 'seller_inquiry_detail' inquiry.id %}" class="btn btn-primary btn-sm">
                                {% if inquiry.seller_response %}
                                    View Details
                                {% else %}
                                    Respond
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="flex items-center justify-between">
            <div class="text-sm text-secondary-700">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} inquiries
            </div>
            <div class="flex space-x-2">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter|urlencode }}{% endif %}{% if listing_filter %}&listing={{ listing_filter|urlencode }}{% endif %}" class="btn btn-secondary btn-sm">Previous</a>
                {% endif %}
                
                <span class="flex items-center px-3 py-2 text-sm text-secondary-700">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter|urlencode }}{% endif %}{% if listing_filter %}&listing={{ listing_filter|urlencode }}{% endif %}" class="btn btn-secondary btn-sm">Next</a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    {% else %}
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <h3 class="text-lg font-medium text-secondary-900 mb-2">No inquiries found</h3>
            <p class="text-secondary-600 mb-6">
                {% if status_filter or listing_filter %}
                    Try adjusting your filters to see more inquiries
                {% else %}
                    Inquiries from buyers will appear here when they're interested in your listings
                {% endif %}
            </p>
            {% if not status_filter and not listing_filter %}
                <a href="{% url 'seller_create_listing' %}" class="btn btn-primary btn-md">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Your First Listing
                </a>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}
{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}Inquiry Details{% endblock %}
{% block active_nav_item %}inquiries{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Seller Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='seller_dashboard' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- My Listings -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_listings" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='My Listings' item_key='listings' %}

    <!-- Create Listing -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_create_listing" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>' label='Create Listing' item_key='create' %}

    <!-- Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='{% url "seller_inquiries" %}' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='Inquiries' item_key='inquiries' %}

    <!-- Divider -->
    <div class="border-t border-secondary-200 my-4"></div>

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='seller_reports' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' label='Help & Support' item_key='help' %>
</div>
{% endblock %}

{% block page_title %}Inquiry from {{ inquiry.buyer.get_full_name|default:inquiry.buyer.username }}{% endblock %}
{% block page_description %}{{ inquiry.land.title }}{% endblock %}

{% block page_actions %}
<a href="{% url 'seller_inquiries' %}" class="btn btn-secondary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
    </svg>
    Back to Inquiries
</a>
{% endblock %}

{% block dashboard_content %}
<div class="max-w-4xl mx-auto">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Original Inquiry -->
            <div class="card">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-secondary-900">Original Inquiry</h3>
                        <div class="flex items-center space-x-2">
                            {% include 'components/inquiry_status_badge.html' with inquiry=inquiry %}
                            <span class="text-sm text-secondary-500">{{ inquiry.created_at|date:"M d, Y \a\t g:i A" }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="flex items-start space-x-4 mb-4">
                        <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-primary-600 font-medium">{{ inquiry.buyer.first_name.0|default:inquiry.buyer.username.0|upper }}</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-secondary-900">{{ inquiry.subject }}</h4>
                            <p class="text-sm text-secondary-600">From {{ inquiry.buyer.get_full_name|default:inquiry.buyer.username }}</p>
                        </div>
                    </div>
                    <div class="prose prose-sm max-w-none">
                        <p class="text-secondary-700 whitespace-pre-wrap">{{ inquiry.message }}</p>
                    </div>
                </div>
            </div>

            <!-- Response Section -->
            {% if inquiry.seller_response %}
                <!-- Existing Response -->
                <div class="card">
                    <div class="card-header">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-secondary-900">Your Response</h3>
                            <span class="text-sm text-secondary-500">{{ inquiry.response_date|date:"M d, Y \a\t g:i A" }}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="flex items-start space-x-4 mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-green-600 font-medium">{{ user.first_name.0|default:user.username.0|upper }}</span>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-secondary-900">{{ user.get_full_name|default:user.username }}</h4>
                                <p class="text-sm text-secondary-600">Seller</p>
                            </div>
                        </div>
                        <div class="prose prose-sm max-w-none">
                            <p class="text-secondary-700 whitespace-pre-wrap">{{ inquiry.seller_response }}</p>
                        </div>
                    </div>
                </div>

                <!-- Update Response -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-secondary-900">Update Your Response</h3>
                        <p class="text-sm text-secondary-600">You can update your response to provide additional information</p>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            {% csrf_token %}
                            <div class="space-y-4">
                                <div>
                                    <label class="form-label">{{ form.seller_response.label }}</label>
                                    {{ form.seller_response }}
                                    {% if form.seller_response.errors %}
                                        <div class="form-error">{{ form.seller_response.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <button type="submit" class="btn btn-primary btn-md">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Update Response
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            {% else %}
                <!-- New Response -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-secondary-900">Send Response</h3>
                        <p class="text-sm text-secondary-600">Respond to this buyer's inquiry about your property</p>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            {% csrf_token %}
                            <div class="space-y-4">
                                <div>
                                    <label class="form-label">{{ form.seller_response.label }}</label>
                                    {{ form.seller_response }}
                                    {% if form.seller_response.errors %}
                                        <div class="form-error">{{ form.seller_response.errors.0 }}</div>
                                    {% endif %}
                                    <p class="text-xs text-secondary-500 mt-1">Be professional and provide helpful information about your property</p>
                                </div>
                                <button type="submit" class="btn btn-primary btn-md">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    Send Response
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Buyer Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Buyer Information</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                                <span class="text-primary-600 font-medium">{{ inquiry.buyer.first_name.0|default:inquiry.buyer.username.0|upper }}</span>
                            </div>
                            <div>
                                <p class="font-semibold text-secondary-900">{{ inquiry.buyer.get_full_name|default:inquiry.buyer.username }}</p>
                                <p class="text-sm text-secondary-600">{{ inquiry.buyer.email }}</p>
                            </div>
                        </div>
                        
                        {% if inquiry.buyer.profile.phone %}
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <span class="text-sm text-secondary-700">{{ inquiry.buyer.profile.phone }}</span>
                            </div>
                        {% endif %}
                        
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8m-8 0V9a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                            </svg>
                            <span class="text-sm text-secondary-700">Member since {{ inquiry.buyer.date_joined|date:"M Y" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Property Details</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        {% if inquiry.land.images.first %}
                            <img src="{{ inquiry.land.images.first.image.url }}" alt="{{ inquiry.land.title }}" class="w-full h-32 object-cover rounded-lg">
                        {% else %}
                            <div class="w-full h-32 bg-secondary-100 rounded-lg flex items-center justify-center">
                                <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        {% endif %}
                        
                        <div>
                            <h4 class="font-semibold text-secondary-900">{{ inquiry.land.title }}</h4>
                            <p class="text-sm text-secondary-600">{{ inquiry.land.location }}</p>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <p class="text-secondary-500">Price</p>
                                <p class="font-semibold text-secondary-900">${{ inquiry.land.price|floatformat:0 }}</p>
                            </div>
                            <div>
                                <p class="text-secondary-500">Size</p>
                                <p class="font-semibold text-secondary-900">{{ inquiry.land.size_acres }} acres</p>
                            </div>
                            <div>
                                <p class="text-secondary-500">Type</p>
                                <p class="font-semibold text-secondary-900">{{ inquiry.land.get_property_type_display }}</p>
                            </div>
                            <div>
                                <p class="text-secondary-500">Status</p>
                                {% include 'components/listing_status_badge.html' with listing=inquiry.land %}
                            </div>
                        </div>
                        
                        <a href="{% url 'seller_edit_listing' inquiry.land.id %}" class="w-full btn btn-secondary btn-sm">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Listing
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Quick Actions</h3>
                </div>
                <div class="card-body space-y-3">
                    <a href="mailto:{{ inquiry.buyer.email }}?subject=Re: {{ inquiry.subject }}" class="w-full btn btn-secondary btn-sm justify-start">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Email Buyer Directly
                    </a>
                    
                    {% if inquiry.buyer.profile.phone %}
                        <a href="tel:{{ inquiry.buyer.profile.phone }}" class="w-full btn btn-secondary btn-sm justify-start">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            Call Buyer
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}